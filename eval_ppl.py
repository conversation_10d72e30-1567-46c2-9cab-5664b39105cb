import torch
import torch.nn as nn
from transformers import AutoModelForCausalLM, AutoTokenizer
from lib.eval import eval_ppl
from lib.model_quantifier import QuantizedLinear
import argparse
import os

def load_quantized_model(model_path, torch_dtype=torch.bfloat16, device_map=None):
    """加载包含量化权重的模型"""
    print(f"正在加载量化模型: {model_path}")

    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        torch_dtype=torch_dtype,
        device_map=device_map
    )

    model_state_path = os.path.join(model_path, "pytorch_model.bin")
    if os.path.exists(model_state_path):
        print("正在加载量化权重...")
        state_dict = torch.load(model_state_path, map_location='cpu')

        # 检查是否包含量化权重
        quantized_keys = [k for k in state_dict.keys() if 'quantized_weight' in k]
        if quantized_keys:
            print(f"发现 {len(quantized_keys)} 个量化层，正在重建量化模型...")
            model = rebuild_quantized_model(model, state_dict)
        else:
            print("未发现量化权重，使用标准加载方式")
            model.load_state_dict(state_dict, strict=False)

    return model

def rebuild_quantized_model(model, state_dict):
    """重建包含量化层的模型"""
    # 找到所有需要替换为量化层的模块
    quantized_modules = {}

    for name in state_dict.keys():
        if 'quantized_weight' in name:
            # 提取模块名称
            module_name = name.replace('.quantized_weight', '')
            quantized_modules[module_name] = True

    print(f"需要量化的模块数量: {len(quantized_modules)}")

    # 替换线性层为量化层
    def replace_with_quantized(module, prefix=''):
        for name, child in module.named_children():
            full_name = f"{prefix}.{name}" if prefix else name

            if full_name in quantized_modules and isinstance(child, nn.Linear):
                # 创建量化层
                quantized_layer = QuantizedLinear(child)

                # 加载量化权重
                quantized_weight_key = f"{full_name}.quantized_weight"
                weight_scale_key = f"{full_name}.weight_scale"

                if quantized_weight_key in state_dict:
                    quantized_layer.quantized_weight.data = state_dict[quantized_weight_key]
                if weight_scale_key in state_dict:
                    quantized_layer.weight_scale.data = state_dict[weight_scale_key]

                # 替换模块
                setattr(module, name, quantized_layer)
                print(f"替换 {full_name} 为量化层")
            else:
                # 递归处理子模块
                replace_with_quantized(child, full_name)

    replace_with_quantized(model)

    # 加载其他非量化权重
    non_quantized_state = {}
    for key, value in state_dict.items():
        if 'quantized_weight' not in key and 'weight_scale' not in key:
            non_quantized_state[key] = value

    model.load_state_dict(non_quantized_state, strict=False)
    print("量化模型重建完成")

    return model

parser = argparse.ArgumentParser()
parser.add_argument('--model', type=str, required=True, help='模型路径')
parser.add_argument('--dataset', type=str, default='wikitext2', help='用于评估的测试集名称')
parser.add_argument('--seqlen', type=int, default=4096, help='模型序列长度')
args = parser.parse_args()

print(f"加载模型: {args.model}")
model = load_quantized_model(
    args.model,
    torch_dtype=torch.bfloat16,
    device_map={"": 0}
)

print(f"加载分词器: {args.model}")
tokenizer = AutoTokenizer.from_pretrained(
    args.model,
    use_fast=True,
    padding_side='right',
    trust_remote_code=True,
    add_eos_token=True,
    add_bos_token=True
)
tokenizer.pad_token = tokenizer.eos_token

model.seqlen = args.seqlen
model.eval()

device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
model.to(device)

print(f"在 {args.dataset} 数据集上评估困惑度 (PPL)...")
ppl = eval_ppl(model, tokenizer, args.dataset, device)
print(f"\n在 {args.dataset} 数据集上的困惑度 (PPL): {ppl}\n")
