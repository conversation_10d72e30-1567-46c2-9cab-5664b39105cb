import numpy as np
import random
import torch
from datasets import load_dataset
import os

# Set seed for reproducibility
def set_seed(seed):
    np.random.seed(seed)
    torch.random.manual_seed(seed)

# Wrapper for tokenized input IDs
class TokenizerWrapper:
    def __init__(self, input_ids):
        self.input_ids = input_ids

# Load and process wikitext2 dataset
def get_wikitext2(nsamples, seed, seqlen, tokenizer):
    local_path = './data/wikitext'
    data_files = {"train": "wikitext-2-raw-v1/train-00000-of-00001.parquet", "test": "wikitext-2-raw-v1/test-00000-of-00001.parquet"}
    try:
        print(f"Attempting to load wikitext2 from local path: {local_path}")
        dataset = load_dataset(local_path, data_files=data_files)
        traindata = dataset['train']
        testdata = dataset['test']
        print("Successfully loaded wikitext2 from local path.")
    except Exception:
        print(f"Could not load wikitext2 from local path. Falling back to Hugging Face Hub.")
        # Load train and test datasets
        traindata = load_dataset('wikitext', 'wikitext-2-raw-v1', split='train')
        testdata = load_dataset('wikitext', 'wikitext-2-raw-v1', split='test')

    # Encode datasets
    trainenc = tokenizer(" ".join(traindata['text']), return_tensors='pt')
    testenc = tokenizer("\n\n".join(testdata['text']), return_tensors='pt')

    # Generate samples from training set
    random.seed(seed)
    trainloader = []
    for _ in range(nsamples):
        i = random.randint(0, trainenc.input_ids.shape[1] - seqlen - 1)
        j = i + seqlen
        inp = trainenc.input_ids[:, i:j]
        tar = inp.clone()
        tar[:, :-1] = -100
        trainloader.append((inp, tar))
    return trainloader, testenc

# Load and process c4 dataset
def get_c4(nsamples, seed, seqlen, tokenizer):
    local_path = './data/c4'
    data_files = {
        'train': 'en/c4-train.00000-of-01024.json.gz',
        'validation': 'en/c4-validation.00000-of-00008.json.gz'
    }
    try:
        print(f"Attempting to load C4 from local path: {local_path}")
        dataset = load_dataset(local_path, data_files=data_files)
        traindata = dataset['train']
        valdata = dataset['validation']
        print("Successfully loaded C4 from local path.")
    except Exception:
        print(f"Could not load C4 from local path. Falling back to Hugging Face Hub.")
        # Load train and validation datasets
        dataset = load_dataset('allenai/c4', 'en', data_files=data_files)
        traindata = dataset['train']
        valdata = dataset['validation']
    
    # Generate samples from training set
    random.seed(seed)
    trainloader = []
    for _ in range(nsamples):
        while True:
            i = random.randint(0, len(traindata) - 1)
            trainenc = tokenizer(traindata[i]['text'], return_tensors='pt')
            if trainenc.input_ids.shape[1] > seqlen:
                break
        i = random.randint(0, trainenc.input_ids.shape[1] - seqlen - 1)
        j = i + seqlen
        inp = trainenc.input_ids[:, i:j]
        tar = inp.clone()
        tar[:, :-1] = -100
        trainloader.append((inp, tar))

    # Prepare validation dataset
    valenc = tokenizer(' '.join(valdata[:1100]['text']), return_tensors='pt')
    valenc = valenc.input_ids[:, :(256 * seqlen)]
    valenc = TokenizerWrapper(valenc)
    return trainloader, valenc

# Load and process ptb dataset
def get_ptb(nsamples, seed, seqlen, tokenizer):
    local_path = './data/ptb_text_only'
    data_files = {
        "train": "penn_treebank/train-00000-of-00001.parquet",
        "test": "penn_treebank/test-00000-of-00001.parquet"
    }
    try:
        print(f"Attempting to load PTB from local path: {local_path}")
        dataset = load_dataset(local_path, data_files=data_files)
        traindata = dataset['train']
        testdata = dataset['test']
        print("Successfully loaded PTB from local path.")
    except Exception:
        print(f"Could not load PTB from local path. Falling back to Hugging Face Hub.")
        dataset = load_dataset('ptb_text_only', 'penn_treebank')
        traindata = dataset['train']
        testdata = dataset['test']

    trainenc = tokenizer(" ".join(traindata['sentence']), return_tensors='pt')
    testenc = tokenizer(" ".join(testdata['sentence']), return_tensors='pt')

    random.seed(seed)
    trainloader = []
    for _ in range(nsamples):
        i = random.randint(0, trainenc.input_ids.shape[1] - seqlen - 1)
        j = i + seqlen
        inp = trainenc.input_ids[:, i:j]
        tar = inp.clone()
        tar[:, :-1] = -100
        trainloader.append((inp, tar))
    return trainloader, testenc
    

# Function to select the appropriate loader based on dataset name
def get_loaders(name, nsamples=128, seed=0, seqlen=2048, tokenizer=None):
    if 'wikitext2' in name:
        return get_wikitext2(nsamples, seed, seqlen, tokenizer)
    if "c4" in name:
        return get_c4(nsamples, seed, seqlen, tokenizer)
    if "ptb" in name:
        return get_ptb(nsamples, seed, seqlen, tokenizer)