import math

import torch
import torch.nn as nn
import transformers

# 禁用TF32以确保数值精度
torch.backends.cuda.matmul.allow_tf32 = False
torch.backends.cudnn.allow_tf32 = False

# import bitsandbytes as bnb

class WrappedGPT:
    """
    GPT层的包装器类，用于收集激活统计信息
    
    主要功能：
    1. 收集输入激活的统计信息（均值、方差、L2范数等）
    2. 为Wanda剪枝提供激活幅度信息
    3. 支持多种统计量的累积计算
    
    与SparseGPT的区别：
    - WrappedGPT主要收集激活统计（一阶和二阶矩）
    - SparseGPT收集Hessian信息（输入的外积）
    - 两者服务于不同的剪枝算法
    """
    
    def __init__(self, layer, initial_method = None, layer_id=0, layer_name="none"):
        """
        初始化层包装器
        
        Args:
            layer: 要包装的神经网络层
            initial_method: 初始化方法（未使用）
            layer_id: 层ID
            layer_name: 层名称
        """
        self.layer = layer
        self.dev = self.layer.weight.device
        self.rows = layer.weight.data.shape[0]     # 输出维度
        self.columns = layer.weight.data.shape[1]  # 输入维度
        self.nsamples = 0  # 处理的样本数量

        self.initial_method = initial_method

        # 用于Wanda剪枝的激活统计
        self.scaler_row = torch.zeros((self.columns), device=self.dev)      # L2范数平方
        self.sum_metric_row = torch.zeros((self.columns), device=self.dev)  # 均值统计
        
        # 激活的均值和方差统计
        self.mean = torch.zeros((self.columns), device=self.dev)
        self.var = torch.zeros((self.columns), device=self.dev)
        self.ntokens = 0  # token数量
        
        self.layer_id = layer_id 
        self.layer_name = layer_name

    def add_batch(self, inp, out):
        """
        添加一个批次的激活数据来更新统计信息
        
        关键统计量：
        1. scaler_row: 每个输入特征的L2范数平方（用于Wanda重要性计算）
        2. mean/var: 激活的均值和方差
        3. sum_metric_row: 激活的累积和
        
        这些统计量用于：
        - Wanda剪枝：scaler_row提供激活幅度信息
        - 特征分析：mean/var用于理解激活分布
        
        Args:
            inp: 输入激活张量
            out: 输出激活张量（未使用）
        """
        if len(inp.shape) == 2:
            inp = inp.unsqueeze(0)
        tmp = inp.shape[0]
        
        # 处理线性层：reshape并转置
        if isinstance(self.layer, nn.Linear):
            if len(inp.shape) == 3:
                inp = inp.reshape((-1, inp.shape[-1]))
            inp = inp.t()  # 转置为 [features, samples]
        inp = inp.type(torch.float32)

        # 计算当前批次的均值和方差
        mean_inp = torch.mean(inp, dim=1, keepdim=True)
        var_inp = torch.var(inp, dim=1, unbiased=False, keepdim=True)
        num_inp = inp.shape[1]
        
        # 使用增量公式更新全局统计量
        if self.ntokens == 0:
            self.var = var_inp
            self.mean = mean_inp
        else:
            # 增量方差计算
            self.var = (self.var * self.ntokens + var_inp * num_inp) / (self.ntokens + num_inp)
            # 增量均值计算
            self.mean = (self.mean * self.ntokens + mean_inp * num_inp) / (self.ntokens + num_inp)
        self.ntokens += num_inp
        
        # 更新样本权重（用于多批次累积）
        self.scaler_row *= self.nsamples / (self.nsamples+tmp)
        self.sum_metric_row *= self.nsamples / (self.nsamples+tmp)
        self.nsamples += tmp

        # 累积激活统计
        # scaler_row: L2范数平方，衡量激活幅度（Wanda算法的关键）
        self.scaler_row += torch.norm(inp, p=2, dim=1) ** 2  / self.nsamples
        # sum_metric_row: 激活累积和
        self.sum_metric_row += torch.sum(inp, dim=1) / self.nsamples

    def free(self):
        """释放内存"""
        self.H = None
        torch.cuda.empty_cache()