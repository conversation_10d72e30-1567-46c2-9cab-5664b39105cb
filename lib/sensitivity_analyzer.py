"""
敏感性分析和动态稀疏度分配模块

负责：
1. 计算层的敏感性分数
2. 基于敏感性的动态稀疏度分配
3. 层级重要性分析
4. 架构感知的稀疏度策略
"""

import math
import torch
import transformers


class SensitivityAnalyzer:
    """敏感性分析器"""
    
    @staticmethod
    def calculate_layer_sensitivity(layer, activation_stats, layer_id, layer_name):
        """
        敏感性计算函数
        
        基于多维特征的层敏感性评估：
        1. 权重分布特征：权重的方差和范围
        2. 激活强度特征：L2范数和动态范围
        3. 信息流特征：基于Rényi熵的激活分布复杂度
        4. 层架构特征：基于层类型的结构重要性
        
        Args:
            layer: 神经网络层
            activation_stats: 激活统计信息
            layer_id: 层ID
            layer_name: 层名称
            
        Returns:
            float: 敏感性分数，越高表示越重要，应分配更低的稀疏度
        """
        if not activation_stats or len(activation_stats.get('l2_norm', [])) == 0:
            return 1.0  # 默认敏感性
            
        # === 1. 权重分布特征分析 ===
        W = layer.weight.data.clone()
        if isinstance(layer, transformers.Conv1D):
            W = W.t()
        W = W.float()
        
        weight_std = torch.std(W).item()
        weight_mean_abs = torch.mean(torch.abs(W)).item()
        weight_max = torch.max(torch.abs(W)).item()
        weight_cv = weight_std / (weight_mean_abs + 1e-8)  # 权重变异系数
        
        # === 2. 激活强度特征 ===
        l2_norm_mean = torch.mean(activation_stats['l2_norm']).item()
        l2_norm_std = torch.std(activation_stats['l2_norm']).item()
        var_mean = torch.mean(activation_stats['var']).item()
        
        # 激活动态范围
        max_val = torch.mean(activation_stats['max_val']).item()
        min_val = torch.mean(activation_stats['min_val']).item()
        activation_range = max_val - min_val + 1e-8
        
        # === 3. 信息流特征 ===
        # 激活的变异系数 - 衡量信息传递的稳定性
        activation_cv = l2_norm_std / (l2_norm_mean + 1e-8)
        
        # === 4. 综合特征计算 ===
        # 使用对数标准化避免极端值
        weight_importance = math.log(1 + weight_cv * 10)  # 权重分布重要性
        activation_importance = math.log(1 + l2_norm_mean * activation_cv)  # 激活重要性
        range_importance = math.log(1 + activation_range)  # 动态范围重要性
        
        # 基础敏感性分数
        base_sensitivity = (weight_importance * 0.4 + 
                           activation_importance * 0.4 + 
                           range_importance * 0.2)
        
        # === 5. 层架构敏感性调整 ===
        arch_weight = SensitivityAnalyzer._get_architecture_weight(layer_name)
        
        # === 6. 层深度调整 ===
        depth_weight = SensitivityAnalyzer._get_depth_weight(layer_id)
        
        # === 7. 最终敏感性分数 ===
        final_sensitivity = base_sensitivity * arch_weight * depth_weight
        
        return final_sensitivity

    @staticmethod
    def _get_architecture_weight(layer_name):
        """获取基于层架构的权重系数"""
        layer_name = layer_name.lower()
        
        # 基于Transformer架构的层重要性先验知识
        if 'q_proj' in layer_name:
            # Query投影：注意力计算的核心，重要性高
            return 1.35
        elif 'k_proj' in layer_name:
            # Key投影：与Query协同工作，重要性高但略低于Query
            return 1.30
        elif 'v_proj' in layer_name:
            # Value投影：携带实际信息，重要性中等偏高
            return 1.25
        elif 'o_proj' in layer_name:
            # Output投影：整合注意力结果，重要性中等，但不应过度剪枝
            return 1.45
        elif 'gate_proj' in layer_name:
            # Gate投影：FFN的门控机制，控制信息流，重要性高
            return 1.30
        elif 'up_proj' in layer_name:
            # Up投影：FFN的扩展层，重要性中等
            return 1.00
        elif 'down_proj' in layer_name:
            # Down投影：FFN的压缩层，可以适度剪枝但不能过度
            return 0.85
        else:
            return 1.0

    @staticmethod
    def _get_depth_weight(layer_id):
        """获取基于层深度的权重系数"""
        # 早期层（浅层）通常更重要，后期层可以承受更多剪枝
        if layer_id < 4:  # 前4层
            return 1.1
        elif layer_id > 20:  # 后层
            return 0.95
        else:
            return 1.0


class DynamicSparsityAllocator:
    """动态稀疏度分配器"""
    
    @staticmethod
    def allocate_sparsity(giraffe_layers, target_sparsity, layer_id=0):
        """
        动态稀疏度分配算法
        
        新的分配策略：
        1. 基于敏感性的保守分配
        2. 避免极端稀疏度分配
        3. 考虑层间平衡和架构约束
        4. 使用更合理的分配范围
        
        Args:
            giraffe_layers: {layer_name: Giraffe对象} 的字典
            target_sparsity: 目标平均稀疏度
            layer_id: 层ID
            
        Returns:
            dict: {layer_name: 分配的稀疏度}
        """
        if not giraffe_layers:
            return {}
            
        # 计算每个子层的敏感性分数
        sensitivities = {}
        layer_params = {}
        total_params = 0
        
        print(f"第{layer_id}层开始动态稀疏度分配...")
        
        for name, giraffe in giraffe_layers.items():
            # 获取激活统计信息
            activation_stats = giraffe.stats_collector.get_stats()
            
            # 计算敏感性
            sensitivity = SensitivityAnalyzer.calculate_layer_sensitivity(
                giraffe.layer, activation_stats, giraffe.layer_id, giraffe.layer_name
            )
            sensitivities[name] = sensitivity
            
            # 计算参数数量
            params = giraffe.layer.weight.numel()
            layer_params[name] = params
            total_params += params
            
            print(f"  {name} 敏感性分数: {sensitivity:.4f}")
        
        # 归一化敏感性分数
        sensitivity_values = list(sensitivities.values())
        min_sens = min(sensitivity_values)
        max_sens = max(sensitivity_values)
        
        if max_sens - min_sens < 1e-6:  # 所有敏感性相同
            print(f"  敏感性分数相近，使用均匀分配")
            return {name: target_sparsity for name in giraffe_layers.keys()}
        
        # === 改进的分配策略 ===
        # 1. 更保守的分配范围
        if target_sparsity <= 0.3:
            max_deviation = 0.10  # 低稀疏度时最大偏差10%
        elif target_sparsity <= 0.5:
            max_deviation = 0.15  # 中等稀疏度时最大偏差15%
        else:
            max_deviation = 0.20  # 高稀疏度时最大偏差20%
        
        min_sparsity = max(0.05, target_sparsity - max_deviation)
        max_sparsity = min(0.75, target_sparsity + max_deviation)  # 限制最大稀疏度为75%
        
        print(f"  稀疏度分配范围: [{min_sparsity:.2%}, {max_sparsity:.2%}]")
        
        # 2. 基于敏感性的分配权重
        sparsity_assignments = {}
        
        for name, sens in sensitivities.items():
            # 归一化敏感性到[0,1]
            normalized_sens = (sens - min_sens) / (max_sens - min_sens)
            
            # 使用更温和的反向映射
            # 敏感性高的层 -> 稀疏度低
            # 敏感性低的层 -> 稀疏度高
            sparsity_factor = 1.0 - 0.5 * normalized_sens  # 范围[0.5, 1.0]
            
            # 计算该层的稀疏度
            layer_sparsity = target_sparsity * sparsity_factor
            
            # 约束到允许范围
            layer_sparsity = max(min_sparsity, min(max_sparsity, layer_sparsity))
            
            sparsity_assignments[name] = layer_sparsity
        
        # 3. 调整以满足总体目标（按参数权重）
        current_avg = sum(sparsity_assignments[name] * layer_params[name] 
                         for name in sparsity_assignments.keys()) / total_params
        
        # 如果总体偏差较大，进行调整
        if abs(current_avg - target_sparsity) > 0.02:  # 偏差超过2%
            adjustment = target_sparsity - current_avg
            print(f"  调整稀疏度分配，偏差: {adjustment:.2%}")
            
            # 按敏感性进行调整：敏感性低的层承担更多调整
            for name in sparsity_assignments.keys():
                sens = sensitivities[name]
                normalized_sens = (sens - min_sens) / (max_sens - min_sens)
                
                # 敏感性低的层承担更多的调整责任
                adjustment_weight = 1.0 - normalized_sens
                layer_adjustment = adjustment * adjustment_weight * 0.5  # 限制调整幅度
                
                adjusted_sparsity = sparsity_assignments[name] + layer_adjustment
                sparsity_assignments[name] = max(min_sparsity, min(max_sparsity, adjusted_sparsity))
        
        # 4. 最终验证和输出
        final_avg = sum(sparsity_assignments[name] * layer_params[name] 
                       for name in sparsity_assignments.keys()) / total_params
        
        print(f"  稀疏度分配结果:")
        total_pruned = 0
        for name, sparsity in sparsity_assignments.items():
            params = layer_params[name]
            pruned = int(params * sparsity)
            total_pruned += pruned
            
            # 标注特别的分配
            note = ""
            if sparsity < target_sparsity - 0.1:
                note = " (保护)"
            elif sparsity > target_sparsity + 0.1:
                note = " (重点剪枝)"
                
            print(f"    {name}: {sparsity:.2%} (预估剪枝 {pruned:,}/{params:,}){note}")
        
        print(f"  实际平均稀疏度: {final_avg:.2%} (偏差: {abs(final_avg - target_sparsity):.2%})")
        
        return sparsity_assignments
