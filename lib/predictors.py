"""
预测器模块

包含各种权重重要性预测算法：
1. 增强预测器（默认）
2. Wanda预测器
3. SNIP预测器
"""

import torch


class PredictorRegistry:
    """预测器注册表，管理所有可用的预测器"""
    
    @staticmethod
    def dolphin_predictor(activation_stats, weight_matrix, layer_info):
        """
        增强预测器

        Args:
            activation_stats: 激活统计信息字典
            weight_matrix: 当前层的权重矩阵 [out_features, in_features]
            layer_info: 层信息（layer_id, layer_name等）
            
        Returns:
            importance_scores: 每个权重的重要性分数 [out_features, in_features]
        """
        # 获取激活统计
        # l2_norm = activation_stats['l2_norm']  # [in_features]
        # variance = activation_stats['var']     # [in_features]
        # mean_abs = torch.abs(activation_stats['mean'])  # [in_features]
        # skewness = torch.abs(activation_stats['skewness'])  # [in_features]
        
        # # 重要性 = |权重| × sqrt(激活的二阶统计)
        # weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]
        # activation_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)  # [1, in_features]
        # wanda_scores = weight_abs * activation_scale  # [out_features, in_features]

        # 获取激活统计
        l2_norm = activation_stats['l2_norm']      # [in_features]
        variance = activation_stats['var']         # [in_features]
        skewness = torch.abs(activation_stats['skewness'])  # [in_features]
        entropy = activation_stats['entropy']      # [in_features]

        # 超参数，用于平衡Rényi熵和L2范数的贡献
        alpha_entropy = 0.2  # Rényi熵权重
        alpha_l2 = 0.7       # L2范数权重

        # 计算 activation_scale，参考原L2方法使用平方根进行平滑
        # 对Rényi熵和L2范数都应用平方根变换，然后加权组合
        entropy_scaled = torch.sqrt(entropy + 1e-8)    # 平滑Rényi熵
        l2_scaled = torch.sqrt(l2_norm + 1e-8)          # 平滑L2范数
        
        # 加权组合两个尺度因子
        activation_scale = (alpha_entropy * entropy_scaled + alpha_l2 * l2_scaled).unsqueeze(0)  # [1, in_features]

        # 重要性 = |权重| × (α·√Rényi熵 + β·√L2范数)
        weight_abs = torch.abs(weight_matrix)  # [out_features, in_features]
        wanda_scores = weight_abs * activation_scale  # [out_features, in_features]
        
        # 高方差 + 高偏度 = 特征更敏感，权重更重要
        sensitivity = variance * (1 + skewness)  # [in_features]
        sensitivity_normalized = sensitivity / (torch.max(sensitivity) + 1e-8)
        sensitivity_matrix = sensitivity_normalized.unsqueeze(0).expand_as(weight_matrix)
        
        # 每个输出神经元的权重分布特性
        weight_mean = torch.mean(weight_abs, dim=1, keepdim=True)  # [out_features, 1]
        
        # 归一化权重重要性：相对于该输出神经元的平均权重
        relative_importance = weight_abs / (weight_mean + 1e-8)
        
        layer_id = layer_info.get('layer_id', 0)
        layer_name = layer_info.get('layer_name', '')
        
        # 根据层类型调整权重策略 - 更精细的差异化处理
        if 'q_proj' in layer_name:
            # Query投影：核心注意力计算，重视激活和权重的协同
            alpha, beta, gamma = 0.45, 0.35, 0.20
        elif 'k_proj' in layer_name:
            # Key投影：与Query协同，稍微偏重激活敏感性
            alpha, beta, gamma = 0.40, 0.40, 0.20
        elif 'v_proj' in layer_name:
            # Value投影：信息载体，平衡各项指标
            alpha, beta, gamma = 0.35, 0.35, 0.30
        elif 'o_proj' in layer_name:
            # Output投影：整合输出，偏重权重分布分析
            alpha, beta, gamma = 0.30, 0.25, 0.45
        elif 'gate_proj' in layer_name:
            # Gate投影：FFN门控，重视激活敏感性（控制信息流）
            alpha, beta, gamma = 0.25, 0.50, 0.25
        elif 'up_proj' in layer_name:
            # Up投影：FFN扩展，平衡处理
            alpha, beta, gamma = 0.35, 0.30, 0.35
        elif 'down_proj' in layer_name:
            # Down投影：FFN压缩，更关注权重分布（可以更激进剪枝）
            alpha, beta, gamma = 0.20, 0.20, 0.60
        else:
            # 默认权重分配
            alpha, beta, gamma = 0.35, 0.30, 0.35
        
        # 归一化各个分量
        wanda_normalized = wanda_scores / (torch.max(wanda_scores) + 1e-8)
        relative_normalized = relative_importance / (torch.max(relative_importance) + 1e-8)
        
        # 加权融合
        importance_scores = (alpha * wanda_normalized + 
                           beta * sensitivity_matrix + 
                           gamma * relative_normalized)
        
        # 使用自适应温度系数来增强重要性分数的差异性
        layer_id = layer_info.get('layer_id', 0)
        
        # 根据层类型和敏感性调整温度系数
        if layer_name:
            if 'q_proj' in layer_name or 'k_proj' in layer_name:
                # 关键注意力层：温和处理，避免过度剪枝
                temperature = 1.5
            elif 'gate_proj' in layer_name:
                # 门控层：保守处理
                temperature = 1.2
            elif 'v_proj' in layer_name or 'up_proj' in layer_name:
                # 中等重要性层：中等温度
                temperature = 2.0
            elif 'o_proj' in layer_name:
                # 输出层：可以较激进
                temperature = 2.5
            elif 'down_proj' in layer_name:
                # 下投影：最激进剪枝
                temperature = 3.0
            else:
                temperature = 2.0
        else:
            temperature = 2.0
        
        # 根据层深度微调温度
        if layer_id < 4:  # 早期层更保守
            temperature *= 0.8
        elif layer_id > 20:  # 后期层更激进
            temperature *= 1.2
        
        importance_flat = importance_scores.flatten()
        importance_enhanced = torch.softmax(importance_flat * temperature, dim=0)
        importance_scores = importance_enhanced.reshape(importance_scores.shape)
        
        return importance_scores

    @staticmethod
    def wanda_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        Wanda预测器
        
        Returns:
            importance_scores: 权重重要性分数
        """
        l2_norm = activation_stats['l2_norm']
        weight_abs = torch.abs(weight_matrix)
        activation_scale = torch.sqrt(l2_norm + 1e-8).unsqueeze(0)
        importance_scores = weight_abs * activation_scale
        return importance_scores
    

    @staticmethod
    def snip_predictor(activation_stats, weight_matrix, layer_info=None):
        """
        SNIP预测器
        
        Returns:
            importance_scores: 权重重要性分数
        """
        # 使用激活方差作为梯度的近似
        variance = activation_stats['var']
        weight_abs = torch.abs(weight_matrix)
        grad_approx = variance.unsqueeze(0).expand_as(weight_matrix)
        importance_scores = weight_abs * grad_approx
        return importance_scores

    @classmethod
    def get_predictor(cls, strategy="dolphin"):
        """
        获取指定策略的预测器

        Args:
            strategy: 预测器策略名称
                - "dolphin": 增强预测器
                - "wanda": Wanda预测器
                - "snip": SNIP预测器

        Returns:
            predictor: 预测器函数
        """
        strategy_map = {
            "dolphin": cls.dolphin_predictor,
            "wanda": cls.wanda_predictor,
            "snip": cls.snip_predictor
        }
        
        if strategy in strategy_map:
            return strategy_map[strategy]
        else:
            available = list(strategy_map.keys())
            raise ValueError(f"未知策略 '{strategy}'，可用策略: {available}")

    @classmethod
    def list_available_strategies(cls):
        """获取所有可用的预测器策略"""
        return ["dolphin", "wanda", "snip"]
