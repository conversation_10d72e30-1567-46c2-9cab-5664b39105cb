# coding=utf-8
# Copyright 2023-present the HuggingFace Inc. team.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from __future__ import annotations

import logging
import re
import warnings
from abc import ABC, abstractmethod
from contextlib import contextmanager
from typing import Any, List, Optional, Union

import torch
from accelerate.hooks import AlignDevicesHook
from accelerate.utils import named_module_tensors, offload_state_dict
from torch import nn
from transformers import PreTrainedModel
from transformers.pytorch_utils import Conv1D

from peft.utils import INCLUDE_LINEAR_LAYERS_SHORTHAND

from ..config import PeftConfig
from ..utils import ModulesToSaveWrapper, _get_submodules


logger = logging.getLogger(__name__)


@contextmanager
def onload_layer(layer):
    r"""
    用于修改包含一个或多个调谐器和基础层的模块的实用程序，这些模块可能被卸载到
    CPU 或磁盘。在执行某些操作之前将模块的子模块移动到执行设备，之后重新分配
    基础层状态字典（如果该层被卸载到磁盘），最后卸载参数。

    如果模块没有卸载的子模块，此函数不执行任何操作。

    Args:
        layer ('torch.nn.Module'):
            包含需要合并的调谐器的层
    """

    offloaded_modules = []
    for name, module in layer.named_modules():
        if name in ["", "base_layer"]:
            continue
        if hasattr(module, "_hf_hook") and isinstance(module._hf_hook, AlignDevicesHook) and module._hf_hook.offload:
            module._hf_hook.pre_forward(module)
            offloaded_modules.append(module)

    base_layer_offload = False
    if hasattr(layer, "base_layer") and (
        hasattr(layer.base_layer, "_hf_hook")
        and isinstance(layer.base_layer._hf_hook, AlignDevicesHook)
        and layer.base_layer._hf_hook.offload
    ):
        if torch.device("meta") in layer.base_layer._hf_hook.original_devices.values():
            # 获取原始磁盘卸载目录的名称
            offload_folder = layer.base_layer._hf_hook.weights_map.dataset.save_folder
        layer.base_layer._hf_hook.pre_forward(layer.base_layer)
        base_layer_offload = True

    yield

    for module in offloaded_modules:
        module._hf_hook.post_forward(module, torch.tensor([]))

    if base_layer_offload:
        # 重新制作权重映射（如果是磁盘卸载，必须在CPU上才能通过memmap将参数发送到磁盘）
        layer.base_layer._hf_hook.weights_map = {
            name: param.to("cpu") for name, param in named_module_tensors(layer.base_layer)
        }
        # 如果原始设备是磁盘，则将权重映射卸载到磁盘
        if torch.device("meta") in layer.base_layer._hf_hook.original_devices.values():
            # 用合并的权重重写目录
            offload_state_dict(offload_folder, layer.base_layer._hf_hook.weights_map)
        layer.base_layer._hf_hook.post_forward(layer.base_layer, torch.tensor([]))


class BaseTuner(nn.Module, ABC):
    r"""
    基础调谐器模型，为所有可注入到 torch.nn.Module 的调谐器提供通用方法和属性

    要添加新的调谐器类，需要重写以下方法：

    - **_prepare_adapter_config**:
        一个私有方法，用于最终准备适配器配置，例如在 `target_modules` 字段缺失的情况下。
    - **_create_and_replace**:
        一个私有方法，用于创建适配器模块并替换目标模块。
    - **_check_target_module_exists**:
        一个私有辅助方法，用于检查传入的模块键名是否与适配器配置中的任何目标模块匹配。

    最简单的方法是查看 `peft.tuners.lora.LoraModel` 类中的实现。

    Attributes:
        model (`torch.nn.Module`):
            将附加适配器调谐器层的模型。
        forward (`Callable`):
            模型的前向传播方法。
        peft_config (`Union[`PeftConfig`, dict[str, PeftConfig]]`):
            适配器配置对象，应该是 `str` 到 `PeftConfig` 对象的字典。也可以
            传递一个 PeftConfig 对象，将创建一个默认名称为 `adapter` 的新适配器，
            或创建一个键为 `adapter_name`、值为该 peft 配置的新字典。
        config (`dict[str, Any]`):
            模型配置对象，应该是 `str` 到 `Any` 对象的字典。
        targeted_module_names (`list[str]`):
            实际被适配的模块名称列表。可用于检查是否正确指定了 `config.target_modules`。
    """

    def __init__(self, model, peft_config: Union[PeftConfig, dict[str, PeftConfig]], adapter_name: str) -> None:
        super().__init__()

        self.model = model
        self.targeted_module_names: list[str] = []

        # 对于高级开发者，如果你想要为你的模型附加多个适配器，
        # 只需为你的模型添加一个 `peft_config` 字典属性。
        if not hasattr(self, "peft_config"):
            self.peft_config = {adapter_name: peft_config} if isinstance(peft_config, PeftConfig) else peft_config
        else:
            logger.info(
                "在模型中已经找到 `peft_config` 属性。这将导致模型中有多个适配器。"
                " 请确保你知道自己在做什么！"
            )
            if isinstance(peft_config, PeftConfig):
                self.peft_config[adapter_name] = peft_config
            else:
                # 用户正在添加一个 PeftConfigs 字典
                self.peft_config.update(peft_config)

        self.active_adapter = adapter_name
        self.inject_adapter(self.model, adapter_name)

        # 将 peft_config 复制到注入的模型中。
        self.model.peft_config = self.peft_config

    @property
    def active_adapters(self) -> list[str]:
        if isinstance(self.active_adapter, str):
            return [self.active_adapter]
        # 已经是一个字符串列表
        return self.active_adapter

    def forward(self, *args: Any, **kwargs: Any):
        return self.model.forward(*args, **kwargs)

    @abstractmethod
    def _prepare_adapter_config(self, peft_config: PeftConfig, model_config: dict) -> PeftConfig:
        r"""
        一个私有方法，用于最终准备适配器配置。对于基于transformers的模型，如果
        `peft_config.target_modules` 为 None，我们可以从
        `TRANSFORMERS_MODELS_TO_XXX_TARGET_MODULES_MAPPING` 自动推断目标模块。
        此方法可以在未来进一步重构，以自动推断所有调谐器模型。

        查看 `peft.tuner.lora.LoraModel._prepare_adapter_config` 作为示例。

        Args:
            peft_config (`str`):
                适配器配置。
            model_config (`str`):
                transformers模型配置，该配置应包含 `model_type` 键。
        """
        ...

    @abstractmethod
    def _check_target_module_exists(peft_config: PeftConfig, key: str) -> bool:
        r"""
        一个辅助私有方法，用于检查传入的模块键名是否与
        `peft_config.target_modules` 列表中的任何目标模块匹配。
        如果匹配，返回 `True`，否则返回 `False`。

        Args:
            peft_config (`PeftConfig`):
                适配器配置。
            key (`str`):
                模块的键名。
        """
        ...

    @abstractmethod
    def _create_and_replace(
        self,
        peft_config: PeftConfig,
        adapter_name: str,
        target: nn.Module,
        target_name: str,
        parent: nn.Module,
        current_key: str,
    ) -> None:
        r"""
        将目标模块就地替换为适配器层。此方法需要被所有调谐器类重写。

        查看 `peft.tuners.lora.LoraModel._create_and_replace` 作为示例。

        Args:
            peft_config (`PeftConfig`):
                适配器配置。
            adapter_name (`str`):
                适配器名称。
            target (`nn.Module`):
                目标模块。
            target_name (`str`):
                目标模块的名称。
            parent (`nn.Module`):
                父模块。
            current_key (`str`):
                当前被适配的目标的键。
        """
        ...

    @abstractmethod
    def _mark_only_adapters_as_trainable(self, model: nn.Module):
        r"""
        一个辅助方法，用于仅将适配器层标记为可训练（即 module.requires_grad = False）。
        所有调谐器类都需要重写此方法以匹配正确的键名。

        查看 `peft.tuners.lora.LoraModel._mark_only_adapters_as_trainable` 作为示例。
        """
        ...

    def _check_new_adapter_config(self, config: PeftConfig) -> None:
        """
        添加新适配器时检查配置的辅助方法。

        如果配置有问题或与现有适配器冲突，抛出ValueError。

        """
        pass

    def inject_adapter(self, model: nn.Module, adapter_name: str):
        r"""
        创建适配器层并用适配器层替换目标模块。如果传递了非提示调谐适配器类，
        此方法会在底层被 `peft.mapping.get_peft_model` 调用。

        相应的PEFT配置直接从BaseTuner类的 `peft_config` 属性中检索。

        Args:
            model (`nn.Module`):
                要调谐的模型。
            adapter_name (`str`):
                适配器名称。
        """
        peft_config = self.peft_config[adapter_name]
        # 注意：如果可能，所有检查都应在此方法的开始执行。
        # 这样，如果出现问题，我们可以尽早抛出异常，而不会让模型
        # 处于糟糕的（半初始化）状态。
        self._check_new_adapter_config(peft_config)

        is_target_modules_in_base_model = False
        key_list = [key for key, _ in model.named_modules()]

        _check_for_modules_to_save = getattr(peft_config, "modules_to_save", None) is not None
        _has_modules_to_save = False

        model_config = getattr(model, "config", {"model_type": "custom"})
        if hasattr(model_config, "to_dict"):
            model_config = model_config.to_dict()

        peft_config = self._prepare_adapter_config(peft_config, model_config)

        # 如果需要，更新 peft_config.target_modules
        peft_config = _maybe_include_all_linear_layers(peft_config, model)

        for key in key_list:
            # 检查是否有modules_to_save的情况
            if _check_for_modules_to_save and any(
                key.endswith(f"{module_to_save}") for module_to_save in peft_config.modules_to_save
            ):
                # 可选地设置要保存的模块
                parent, target, target_name = _get_submodules(model, key)

                if not isinstance(target, ModulesToSaveWrapper):
                    new_module = ModulesToSaveWrapper(target, adapter_name)
                    setattr(parent, target_name, new_module)
                else:
                    target.update(adapter_name)

                _has_modules_to_save = True
                continue

            if not self._check_target_module_exists(peft_config, key):
                continue

            self.targeted_module_names.append(key)
            is_target_modules_in_base_model = True
            parent, target, target_name = _get_submodules(model, key)
            self._create_and_replace(peft_config, adapter_name, target, target_name, parent, current_key=key)

        if not is_target_modules_in_base_model:
            raise ValueError(
                f"Target modules {peft_config.target_modules} not found in the base model. "
                f"Please check the target modules and try again."
            )

        self._mark_only_adapters_as_trainable(model)

        if self.peft_config[adapter_name].inference_mode:
            for n, p in model.named_parameters():
                if adapter_name in n:
                    p.requires_grad = False

        if _has_modules_to_save:
            if not hasattr(model, "modules_to_save"):
                model.modules_to_save = set(peft_config.modules_to_save)
            else:
                model.modules_to_save.update(set(peft_config.modules_to_save))

    def merge_adapter(self, adapter_names: Optional[list[str]] = None) -> None:
        """
        此方法将适配器层合并到基础模型中。

        合并适配器可以加速前向传播。适配器权重的副本仍保存在内存中，
        这是取消合并适配器所必需的。要在不将适配器权重保留在内存中的情况下
        合并适配器权重，请调用 `merge_and_unload`。

        Args:
            safe_merge (`bool`, *可选*):
                如果为 `True`，合并操作将在原始权重的副本中执行，并在合并权重之前检查NaN。
                这在您想要检查合并操作是否会产生NaN时很有用。默认为 `False`。
            adapter_names (`list[str]`, *可选*):
                应该合并的适配器名称列表。如果为 `None`，将合并所有活动适配器。
                默认为 `None`。
        """
        for module in self.model.modules():
            if isinstance(module, BaseTunerLayer):
                with onload_layer(module):
                    module.merge(adapter_names=adapter_names)

    def unmerge_adapter(self):
        """
        此方法从基础模型中取消合并所有已合并的适配器层。
        """
        for module in self.model.modules():
            if isinstance(module, BaseTunerLayer):
                with onload_layer(module):
                    module.unmerge()

    def _unloading_checks(self, adapter_names: Optional[List[str]]):
        adapters_to_consider = adapter_names or self.active_adapters
        is_modules_to_save_available = any(
            self.peft_config[adapter].modules_to_save for adapter in adapters_to_consider
        )
        if is_modules_to_save_available and len(adapters_to_consider) > 1:
            raise ValueError("Cannot unload multiple adapters that specify `modules_to_save`.")


class BaseTunerLayer(ABC):
    r"""
    调谐器层混入类，为所有调谐器提供通用方法和属性。

    Args:
        is_plugable (`bool`, *可选*):
            适配器层是否可以插入到任何pytorch模块
        active_adapters (Union[List[`str`], `str`], *可选*):
            活动适配器的名称。
    """

    active_adapter = None

    # 所有可能包含适配器（可训练）权重的层名称
    adapter_layer_names: tuple[str] = ()
    # 所有可能包含适配器相关参数的其他参数名称
    other_param_names: tuple[str] = ()

    # 指示是否应禁用所有适配器
    _disable_adapters: bool = False

    # 当前活动的适配器
    _active_adapter: str | list[str] = "default"

    # 列出所有已合并的适配器
    merged_adapters: list[str] = []

    def get_base_layer(self) -> nn.Module:
        """
        （递归地）获取base_layer。

        这对于调谐器层包装另一个调谐器层的情况是必要的。

        """
        base_layer = self
        while hasattr(base_layer, "base_layer"):
            base_layer = base_layer.base_layer
        return base_layer

    @property
    def weight(self) -> torch.Tensor:
        # 某些transformers代码需要这个，例如对于T5，权重的访问方式是：
        #     self.wo.weight
        # 其中"wo"是适配器层。
        # https://github.com/huggingface/transformers/blob/78f6ed6c70b29c1560780e3869a7ad4c6b3d2710/src/transformers
        # /models/t5/modeling_t5.py#L292
        base_layer = self.get_base_layer()
        if hasattr(base_layer, "qweight"):
            # QuantLinear
            weight = base_layer.qweight
        else:
            # 其他层
            weight = base_layer.weight
        return weight

    def merge(self, safe_merge: bool = False, adapter_names: Optional[list[str]] = None) -> None:
        raise NotImplementedError

    def unmerge(self) -> None:
        raise NotImplementedError

    @property
    def merged(self) -> bool:
        return bool(self.merged_adapters)

    @property
    def disable_adapters(self) -> bool:
        # 使用属性确保disable_adapters不会被直接设置，而是使用enable_adapters方法
        return self._disable_adapters

    @property
    def active_adapter(self) -> str:
        # 使用属性确保active_adapter不会被直接设置，而是使用set_adapter方法
        return self._active_adapter

    @property
    def active_adapters(self):
        if isinstance(self.active_adapter, str):
            return [self.active_adapter]
        # 已经是一个字符串列表
        return self.active_adapter

    def enable_adapters(self, enabled: bool) -> None:
        """切换适配器的启用和禁用

        负责设置适配器权重的requires_grad标志。

        Args:
            enabled (bool): True表示启用适配器，False表示禁用适配器
        """
        if enabled:
            self.set_adapter(self.active_adapters)
            self._disable_adapters = False
        else:
            # 禁用所有适配器层的梯度
            for layer_name in self.adapter_layer_names:
                layer = getattr(self, layer_name)
                layer.requires_grad_(False)
            self._disable_adapters = True

    def set_adapter(self, adapter_names: str | list[str]) -> None:
        """设置活动适配器。

        Args:
            adapter_name (`str` or `List[str]`): 要激活的适配器名称。
        """
        if isinstance(adapter_names, str):
            adapter_names = [adapter_names]

        # 在非活动适配器上禁用梯度，在活动适配器上启用梯度
        for layer_name in self.adapter_layer_names:
            module_dict = getattr(self, layer_name)
            for key, layer in module_dict.items():
                if key in adapter_names:
                    # 注意：这里可能没有一个层会被调用requires_grad_(True)。这可能
                    # 发生在激活完全不同的适配器层时。
                    layer.requires_grad_(True)
                else:
                    layer.requires_grad_(False)

        self._active_adapter = adapter_names

    def _all_available_adapter_names(self) -> list[str]:
        """返回所有可用适配器名称的排序列表"""
        adapter_names = set()
        for name in self.adapter_layer_names + self.other_param_names:
            # 我们检查每个可能的属性，如果它是dict或ModuleDict，我们假设键是适配器
            # 名称
            attr = getattr(self, name)
            if hasattr(attr, "keys"):
                adapter_names.update(attr.keys())
        return sorted(adapter_names)

    def delete_adapter(self, adapter_name: str) -> None:
        """
        从层中删除适配器

        这应该在所有适配器层上调用，否则我们会得到不一致的状态。

        如果删除的适配器是活动适配器，此方法还会设置一个新的活动适配器。
        重要的是以确定性方式选择新适配器，以便在所有层上选择相同的适配器。

        Args:
            adapter_name (`str`): 要删除的适配器的名称

        """
        for attr in self.adapter_layer_names + self.other_param_names:
            if adapter_name in getattr(self, attr):
                del getattr(self, attr)[adapter_name]

        if adapter_name in self.active_adapters:
            # choose a new active adapter
            active_adapters = self.active_adapters[:]
            active_adapters.remove(adapter_name)
            if active_adapters:
                self.set_adapter(active_adapters)
            else:
                # 没有活动适配器剩余，设置新的默认适配器
                # 这里我们获取所有现有适配器名称的列表并选择第一个
                remaining_adapters = self._all_available_adapter_names()
                if not remaining_adapters:
                    self.set_adapter([])
                else:
                    new_active_adapter = remaining_adapters[0]
                    warnings.warn(
                        f"适配器 {adapter_name} 处于活动状态，现在已被删除。将活动适配器设置为 "
                        f"{new_active_adapter}。"
                    )
                    self.set_adapter(remaining_adapters[0])


def check_target_module_exists(config, key: str) -> bool | re.Match[str] | None:
    """检查传入的模块键名是否与adapter_config中的任何目标模块匹配的辅助方法。

    Args:
        config (`LoraConfig` | `LycorisConfig`): 用于匹配目标模块的配置
        key (`str`): 在配置中搜索任何匹配项的键

    Returns:
        `bool` | `re.Match[str]` | `None`: 如果键匹配配置中的任何目标模块，返回True或匹配对象，
        如果没有找到匹配项，返回False或None
    """
    if isinstance(config.target_modules, str):
        target_module_found = re.fullmatch(config.target_modules, key)
    elif key in config.target_modules:
        # 此模块直接在target_modules中指定
        target_module_found = True
    else:
        target_module_found = any(key.endswith(f".{target_key}") for target_key in config.target_modules)

        layer_indexes = getattr(config, "layers_to_transform", None)
        layers_pattern = getattr(config, "layers_pattern", None)

        is_using_layer_indexes = layer_indexes is not None and (
            len(layer_indexes) != 0 if isinstance(layer_indexes, list) else True
        )
        if is_using_layer_indexes and target_module_found:
            layer_index = None
            # TODO: 空的layers_pattern（None, []或""）应该如何表现仍不清楚
            # 目前，空的layers_pattern意味着任何层模式都可以
            if layers_pattern is None or len(layers_pattern) == 0:
                layer_index = re.match(r".*\.[^.]*\.(\d+)\.", key)
            else:
                layers_pattern = [layers_pattern] if isinstance(layers_pattern, str) else layers_pattern
                for pattern in layers_pattern:
                    layer_index = re.match(r".*\.{layer}\.(\d+)\.".format(layer=pattern), key)
                    if layer_index is not None:
                        break

            if layer_index is None:
                target_module_found = False
            else:
                layer_index = int(layer_index.group(1))
                if isinstance(layer_indexes, int):
                    target_module_found = layer_index == layer_indexes
                else:
                    target_module_found = layer_index in layer_indexes

    return target_module_found


def inspect_matched_modules(tuner: BaseTuner, adapter_name: str = "default") -> dict:
    """
    检查PEFT模型和给定适配器的匹配和未匹配模块集的辅助函数。
    """
    config = tuner.peft_config[adapter_name]
    key_list = [key for key, _ in tuner.model.named_modules()]
    module_dict = {"matched": [], "unmatched": []}
    for key in key_list:
        if tuner._check_target_module_exists(config, key):
            module_dict["matched"].append(key)
        else:
            module_dict["unmatched"].append(key)
    return module_dict


def _maybe_include_all_linear_layers(peft_config: PeftConfig, model: nn.Module) -> PeftConfig:
    """
    如果提供为'all-linear'，则将`target_modules`更新为所有线性/Conv1D层的辅助函数。
    改编自QLoRA存储库：https://github.com/artidoro/qlora/blob/main/qlora.py
    """

    # 如果`target_modules`是字符串，转换为小写并检查是否匹配"all-linear"
    if not (
        isinstance(peft_config.target_modules, str)
        and peft_config.target_modules.lower() == INCLUDE_LINEAR_LAYERS_SHORTHAND
    ):
        return peft_config

    if not isinstance(model, PreTrainedModel):
        raise ValueError(
            f"只有PreTrainedModel的实例支持 `target_modules={INCLUDE_LINEAR_LAYERS_SHORTHAND!r}`"
        )

    linear_classes = (torch.nn.Linear, Conv1D)

    linear_module_names = set()
    for name, module in model.named_modules():
        # 与所有线性类匹配。
        if isinstance(module, linear_classes):
            names = name.rsplit(".", 1)[-1]  # 获取基本名称
            linear_module_names.add(names)

    # 忽略文本生成模型的最后分类头
    output_emb = model.get_output_embeddings()
    if output_emb is not None:
        last_module_name = [name for name, module in model.named_modules() if module is output_emb][0]
        linear_module_names -= {last_module_name}
    peft_config.target_modules = linear_module_names
    return peft_config
