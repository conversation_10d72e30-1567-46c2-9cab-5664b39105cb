"""
Centered Kernel Alignment (CKA) 相似性分析模块

CKA是一种衡量两个特征表示相似性的指标，特别适用于神经网络层间比较。

核心原理：
1. CKA基于Hilbert-Schmidt独立性准则（HSIC）
2. 通过核函数将特征映射到再生核希尔伯特空间（RKHS）
3. 计算两个特征表示在该空间中的标准化内积

在稀疏化中的作用：
1. 计算各层特征的相似性矩阵
2. 识别冗余层和重要层
3. 指导每层稀疏度的分配策略
4. 相似性高的层可以承受更高的稀疏度

数学公式：
CKA(X,Y) = HSIC(X,Y) / sqrt(HSIC(X,X) * HSIC(Y,Y))
其中 HSIC(X,Y) = trace(KXHKYH) / (n-1)^2
"""

import numpy as np
import torch
import sys

def gram_linear(x):
    """
    计算线性核的Gram矩阵
    
    线性核：K(xi, xj) = xi^T * xj
    这是最简单的核函数，直接计算特征向量的内积
    
    Args:
        x: 特征矩阵 [num_examples, num_features] (PyTorch张量)

    Returns:
        Gram矩阵 [num_examples, num_examples] (PyTorch张量)
    """
    return torch.mm(x, x.t())

def center_gram(gram, unbiased=False):
    """
    对Gram矩阵进行中心化处理
    
    中心化的目的：
    1. 移除特征的均值影响，关注相对模式
    2. 使CKA对特征的平移不变
    3. 确保HSIC的正确计算
    
    数学公式：
    centered_K = HKH，其中H = I - (1/n)*11^T 是中心化矩阵
    
    Args:
        gram: 对称的Gram矩阵 [num_examples, num_examples] (PyTorch张量)
        unbiased: 是否使用无偏估计（可能产生负值）

    Returns:
        中心化后的对称矩阵
    """
    if not torch.allclose(gram, gram.t(), atol=1e-8):
        raise ValueError('Input must be a symmetric matrix.')
    gram = gram.clone()
    n = gram.size(0)

    if unbiased:
        # 无偏估计：对角线设为0，调整分母
        torch.fill_diagonal_(gram, 0)
        means = torch.sum(gram, dim=0, dtype=torch.float64) / (n - 2)
        means -= torch.sum(means) / (2 * (n - 1))
        gram = gram - means.unsqueeze(0) - means.unsqueeze(1)
        torch.fill_diagonal_(gram, 0)
    else:
        # 有偏估计：标准中心化
        means = torch.mean(gram, dim=0, dtype=torch.float64)
        means -= torch.mean(means) / 2
        gram = gram - means.unsqueeze(0) - means.unsqueeze(1)

    return gram

def cka(gram_x, gram_y, debiased=False):
    """
    计算两个特征表示之间的CKA相似性
    
    CKA的优势：
    1. 对正交变换不变（旋转、反射）
    2. 对各向同性缩放不变
    3. 能够捕获非线性相关性
    4. 返回[0,1]范围内的相似性分数
    
    在稀疏化中的应用：
    - 识别功能相似的层
    - 指导层重要性分配
    - 优化剪枝策略
    
    Args:
        gram_x: 第一个特征的Gram矩阵 [num_examples, num_examples]
        gram_y: 第二个特征的Gram矩阵 [num_examples, num_examples]  
        debiased: 是否使用无偏估计

    Returns:
        CKA相似性分数 (标量，范围[0,1])
    """
    # 中心化Gram矩阵
    gram_x = center_gram(gram_x, unbiased=debiased)
    gram_y = center_gram(gram_y, unbiased=debiased)

    # 计算HSIC：trace(K1 * K2)
    scaled_hsic = (gram_x.view(-1) * gram_y.view(-1)).sum()

    # 计算归一化因子：sqrt(HSIC(X,X) * HSIC(Y,Y))
    normalization_x = torch.norm(gram_x)
    normalization_y = torch.norm(gram_y)
    
    # 返回标准化的CKA分数
    return scaled_hsic / (normalization_x * normalization_y)



def gram_rbf(x, threshold=1.0):
    """Compute Gram (kernel) matrix for an RBF kernel.

  Args:
    x: A num_examples x num_features matrix of features.
    threshold: Fraction of median Euclidean distance to use as RBF kernel
      bandwidth. (This is the heuristic we use in the paper. There are other
      possible ways to set the bandwidth; we didn't try them.)

  Returns:
    A num_examples x num_examples Gram matrix of examples.
  """
    dot_products = x.dot(x.T)
    sq_norms = np.diag(dot_products)
    sq_distances = -2 * dot_products + sq_norms[:, None] + sq_norms[None, :]
    sq_median_distance = np.median(sq_distances)
    return np.exp(-sq_distances / (2 * threshold ** 2 * sq_median_distance))


def _debiased_dot_product_similarity_helper(
        xty, sum_squared_rows_x, sum_squared_rows_y, squared_norm_x, squared_norm_y,
        n):
    """Helper for computing debiased dot product similarity (i.e. linear HSIC)."""
    # This formula can be derived by manipulating the unbiased estimator from
    # Song et al. (2007).
    return (
            xty - n / (n - 2.) * sum_squared_rows_x.dot(sum_squared_rows_y)
            + squared_norm_x * squared_norm_y / ((n - 1) * (n - 2)))


def feature_space_linear_cka(features_x, features_y, debiased=False):
  """Compute CKA with a linear kernel, in feature space.

  This is typically faster than computing the Gram matrix when there are fewer
  features than examples.

  Args:
    features_x: A num_examples x num_features matrix of features.
    features_y: A num_examples x num_features matrix of features.
    debiased: Use unbiased estimator of dot product similarity. CKA may still be
      biased. Note that this estimator may be negative.

  Returns:
    The value of CKA between X and Y.
  """
  features_x = features_x - np.mean(features_x, 0, keepdims=True)
  features_y = features_y - np.mean(features_y, 0, keepdims=True)

  dot_product_similarity = np.linalg.norm(features_x.T.dot(features_y)) ** 2
  normalization_x = np.linalg.norm(features_x.T.dot(features_x))
  normalization_y = np.linalg.norm(features_y.T.dot(features_y))

  if debiased:
    n = features_x.shape[0]
    # Equivalent to np.sum(features_x ** 2, 1) but avoids an intermediate array.
    sum_squared_rows_x = np.einsum('ij,ij->i', features_x, features_x)
    sum_squared_rows_y = np.einsum('ij,ij->i', features_y, features_y)
    squared_norm_x = np.sum(sum_squared_rows_x)
    squared_norm_y = np.sum(sum_squared_rows_y)

    dot_product_similarity = _debiased_dot_product_similarity_helper(
        dot_product_similarity, sum_squared_rows_x, sum_squared_rows_y,
        squared_norm_x, squared_norm_y, n)
    normalization_x = np.sqrt(_debiased_dot_product_similarity_helper(
        normalization_x ** 2, sum_squared_rows_x, sum_squared_rows_x,
        squared_norm_x, squared_norm_x, n))
    normalization_y = np.sqrt(_debiased_dot_product_similarity_helper(
        normalization_y ** 2, sum_squared_rows_y, sum_squared_rows_y,
        squared_norm_y, squared_norm_y, n))

  return dot_product_similarity / (normalization_x * normalization_y)