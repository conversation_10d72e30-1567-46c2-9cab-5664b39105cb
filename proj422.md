# Intelligent-Sparse-Decision-LLM
通过分析大语言模型回答时智能体的参数调用，实现操作系统层的agent的本地模型稀疏化
## 项目名称
基于参数活跃性感知的大模型动态内存加载系统设计

## 项目描述
在大模型推理场景中，传统全量参数加载机制导致内存占用居高不下。研究表明，单次前向计算实际参与的参数仅占模型总量的20%-40%，但现有系统仍将全部参数驻留内存，造成大量冷数据占据宝贵内存资源，引发频繁换页操作并增加计算延迟。

本项目要求参赛者设计一种基于参数活跃性感知的动态内存管理系统，通过实时追踪模型参数的访问模式，智能识别当前推理任务所需的活跃参数子集，实现按需加载/卸载模型参数。系统需在操作系统层建立参数访问热力模型，预测参数激活路径，在保证模型输出质量的前提下，显著降低内存占用量与换页频率。

## 项目特征
1. **参数活跃性分析**：通过多种可行方法（如统计分析、访问模式追踪等）建立参数重要性评估机制
2. **智能参数调度策略**：根据参数活跃度预测，实现高效的参数加载与释放机制
3. **零质量损失**：通过参数完整性校验确保动态加载不改变模型输出语义
4. **透明化集成**：兼容PyTorch、TensorFlow等框架的自动微分机制

## 预期目标
### 1. 核心优化能力
- 实现大模型推理内存占用量降低10%-30%（同等硬件条件下）
- 将换页操作频率降低至传统方案的1/3以下
- 保障动态加载场景下PPL（困惑度）指标波动不超过±0.5%

### 2. 系统兼容性
- 支持HuggingFace Transformers、ONNX Runtime等主流推理引擎
- 适配Attention Offloading、Checkpoint重计算等优化技术
- 提供Python接口，支持10亿级以上参数模型的实时加载

### 3. 可观测性设计
- 开发参数活跃度热力图可视化组件
- 实时展示内存压缩率、换页时间占比等核心指标
- 提供参数加载轨迹回溯功能，支持计算路径异常诊断

选择本项目的同学也可提出自己的新想法，得到导师认可支持后亦可加入预期目标或进阶特性。

## 参考资料

1. **开源项目与工具链**
   - [DeepSeek-R1](https://huggingface.co/deepseek-ai) - 基于强化学习的推理优化框架，支持6个蒸馏版
   - [vLLM 0.4.1](https://github.com/vllm-project/vllm) - 新增DeepSeek MLA融合注意力内核，支持TPU分布式推理
   - [Perf-LLM](https://perf.wiki.kernel.org/) - 集成Linux 6.9+内核的LLM专用性能分析工具

2. **框架与生态**
   - [HuggingFace Transformers 4.51](https://github.com/huggingface/transformers) - 原生支持256K上下文与Spectrum微调方法
   - [llama.cpp 3.8](https://github.com/ggerganov/llama.cpp) - 新增SSE4.2指令优化，MMProj多模态接口
   - [TensorRT-LLM 0.8](https://github.com/NVIDIA/TensorRT-LLM) - 支持W8A8量化与MoE自动并行策略
   - [Cohere Command A](https://cohere.com) - 111B开源商业模型，支持多工具调用与安全模式 

## 所属赛道
2025全国大学生操作系统比赛的"OS功能设计"赛道

## 难度
高等（需深入理解大模型参数特性与内存管理协同机制）

## 项目导师
- 姓名：宫晓利
- 单位：南开大学
- email: <EMAIL>