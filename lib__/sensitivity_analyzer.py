"""
敏感性分析和动态稀疏度分配模块

负责：
1. 基于激活相关性的层重要性评估（ALS方法）
2. 信息正交性分析和层间相关性计算
3. 自适应稀疏度分配算法
4. 架构感知的稀疏度策略
5. 多维度敏感性分析

基于最新研究：
- Adaptive Layer Sparsity (ALS) via Activation Correlation Assessment
- Information Orthogonality for Layer Importance Estimation
- Dynamic Sparsity Allocation with Linear Optimization
"""

import math
import torch
import torch.nn.functional as F
import transformers
from typing import Dict, List, Tuple, Optional


class ActivationCorrelationAnalyzer:
    """
    激活相关性分析器 - 基于ALS方法的层重要性评估

    实现最新的Adaptive Layer Sparsity (ALS)方法：
    1. 激活相关性矩阵计算
    2. 信息正交性评估
    3. 层间依赖关系分析
    """

    @staticmethod
    def compute_activation_correlation(activation_stats_list: List[Dict], layer_names: List[str]) -> torch.Tensor:
        """
        计算层间激活相关性矩阵

        基于信息正交性概念，计算不同层之间的激活相关性，
        用于评估层的重要性和冗余度。

        Args:
            activation_stats_list: 所有层的激活统计信息列表
            layer_names: 层名称列表

        Returns:
            correlation_matrix: 层间相关性矩阵 [num_layers, num_layers]
        """
        num_layers = len(activation_stats_list)
        if num_layers == 0:
            return torch.eye(1)

        # 提取每层的特征向量
        layer_features = []
        for stats in activation_stats_list:
            if not stats or len(stats.get('l2_norm', [])) == 0:
                # 如果没有统计信息，使用默认特征
                feature_dim = 128  # 默认特征维度
                features = torch.ones(feature_dim) * 0.5
            else:
                # 构建多维特征向量
                l2_norm = stats['l2_norm']
                variance = stats['var']
                entropy = stats.get('entropy', torch.zeros_like(l2_norm))

                # 归一化特征
                l2_norm_norm = F.normalize(l2_norm.unsqueeze(0), p=2, dim=1).squeeze(0)
                var_norm = F.normalize(variance.unsqueeze(0), p=2, dim=1).squeeze(0)
                entropy_norm = F.normalize(entropy.unsqueeze(0), p=2, dim=1).squeeze(0)

                # 组合特征向量
                features = torch.cat([l2_norm_norm, var_norm, entropy_norm], dim=0)

            layer_features.append(features)

        # 计算相关性矩阵
        correlation_matrix = torch.zeros(num_layers, num_layers)

        for i in range(num_layers):
            for j in range(num_layers):
                if i == j:
                    correlation_matrix[i, j] = 1.0
                else:
                    # 计算余弦相似度作为相关性度量
                    feat_i = layer_features[i]
                    feat_j = layer_features[j]

                    # 确保特征维度一致
                    min_dim = min(feat_i.size(0), feat_j.size(0))
                    feat_i = feat_i[:min_dim]
                    feat_j = feat_j[:min_dim]

                    correlation = F.cosine_similarity(feat_i.unsqueeze(0), feat_j.unsqueeze(0))
                    correlation_matrix[i, j] = correlation.item()

        return correlation_matrix

    @staticmethod
    def compute_information_orthogonality(correlation_matrix: torch.Tensor) -> torch.Tensor:
        """
        计算信息正交性分数

        基于相关性矩阵计算每层的信息正交性，
        正交性高的层包含更独特的信息，重要性更高。

        Args:
            correlation_matrix: 层间相关性矩阵

        Returns:
            orthogonality_scores: 每层的正交性分数
        """
        num_layers = correlation_matrix.size(0)
        orthogonality_scores = torch.zeros(num_layers)

        for i in range(num_layers):
            # 计算该层与其他层的平均相关性
            other_correlations = correlation_matrix[i, :]
            other_correlations[i] = 0  # 排除自相关

            # 正交性 = 1 - 平均相关性
            avg_correlation = torch.mean(torch.abs(other_correlations))
            orthogonality_scores[i] = 1.0 - avg_correlation

        return orthogonality_scores


class SensitivityAnalyzer:
    """
    增强的敏感性分析器 - 融合传统方法和最新ALS方法
    """

    @staticmethod
    def calculate_layer_sensitivity(layer, activation_stats, layer_id, layer_name,
                                  correlation_info: Optional[Dict] = None):
        """
        多维度层敏感性计算

        融合传统敏感性分析和最新的激活相关性方法：
        1. 传统权重和激活特征分析
        2. 激活相关性和信息正交性
        3. 架构感知的重要性评估
        4. 层深度和位置影响

        Args:
            layer: 神经网络层
            activation_stats: 激活统计信息
            layer_id: 层ID
            layer_name: 层名称
            correlation_info: 相关性分析信息（可选）

        Returns:
            float: 综合敏感性分数
        """
        if not activation_stats or len(activation_stats.get('l2_norm', [])) == 0:
            return 1.0  # 默认敏感性
            
        # === 1. 传统权重分布特征分析 ===
        W = layer.weight.data.clone()
        if isinstance(layer, transformers.Conv1D):
            W = W.t()
        W = W.float()

        weight_std = torch.std(W).item()
        weight_mean_abs = torch.mean(torch.abs(W)).item()
        weight_max = torch.max(torch.abs(W)).item()
        weight_cv = weight_std / (weight_mean_abs + 1e-8)  # 权重变异系数

        # === 2. 传统激活强度特征 ===
        l2_norm_mean = torch.mean(activation_stats['l2_norm']).item()
        l2_norm_std = torch.std(activation_stats['l2_norm']).item()
        var_mean = torch.mean(activation_stats['var']).item()

        # 激活动态范围
        max_val = torch.mean(activation_stats['max_val']).item()
        min_val = torch.mean(activation_stats['min_val']).item()
        activation_range = max_val - min_val + 1e-8

        # === 3. 信息流特征（熵分析）===
        entropy_mean = torch.mean(activation_stats.get('entropy', torch.zeros_like(activation_stats['l2_norm']))).item()
        activation_cv = l2_norm_std / (l2_norm_mean + 1e-8)

        # === 4. 激活相关性特征（ALS方法）===
        correlation_score = 1.0  # 默认值
        orthogonality_score = 1.0  # 默认值

        if correlation_info:
            correlation_score = correlation_info.get('correlation_score', 1.0)
            orthogonality_score = correlation_info.get('orthogonality_score', 1.0)

        # === 5. 综合特征计算 ===
        # 传统特征（权重40%）
        weight_importance = math.log(1 + weight_cv * 10)
        activation_importance = math.log(1 + l2_norm_mean * activation_cv)
        range_importance = math.log(1 + activation_range)
        entropy_importance = math.log(1 + entropy_mean * 5)

        traditional_score = (weight_importance * 0.3 +
                           activation_importance * 0.3 +
                           range_importance * 0.2 +
                           entropy_importance * 0.2)

        # ALS特征（权重60% - 更重要）
        als_score = (orthogonality_score * 0.7 + correlation_score * 0.3)

        # 综合基础敏感性
        base_sensitivity = traditional_score * 0.4 + als_score * 0.6

        # === 6. 层架构敏感性调整 ===
        arch_weight = SensitivityAnalyzer._get_architecture_weight(layer_name)

        # === 7. 层深度调整 ===
        depth_weight = SensitivityAnalyzer._get_depth_weight(layer_id)

        # === 8. 最终敏感性分数 ===
        final_sensitivity = base_sensitivity * arch_weight * depth_weight

        return final_sensitivity

    @staticmethod
    def _get_architecture_weight(layer_name):
        """获取基于层架构的权重系数"""
        layer_name = layer_name.lower()
        
        # 基于Transformer架构的层重要性先验知识
        if 'q_proj' in layer_name:
            # Query投影：注意力计算的核心，重要性高
            return 1.35
        elif 'k_proj' in layer_name:
            # Key投影：与Query协同工作，重要性高但略低于Query
            return 1.30
        elif 'v_proj' in layer_name:
            # Value投影：携带实际信息，重要性中等偏高
            return 1.25
        elif 'o_proj' in layer_name:
            # Output投影：整合注意力结果，重要性中等，但不应过度剪枝
            return 1.45
        elif 'gate_proj' in layer_name:
            # Gate投影：FFN的门控机制，控制信息流，重要性高
            return 1.30
        elif 'up_proj' in layer_name:
            # Up投影：FFN的扩展层，重要性中等
            return 1.00
        elif 'down_proj' in layer_name:
            # Down投影：FFN的压缩层，可以适度剪枝但不能过度
            return 0.85
        else:
            return 1.0

    @staticmethod
    def _get_depth_weight(layer_id):
        """获取基于层深度的权重系数"""
        # 早期层（浅层）通常更重要，后期层可以承受更多剪枝
        if layer_id < 4:  # 前4层
            return 1.1
        elif layer_id > 20:  # 后层
            return 0.95
        else:
            return 1.0


class AdaptiveSparsityAllocator:
    """
    自适应稀疏度分配器 - 基于ALS方法的改进版本

    实现最新的自适应稀疏度分配算法：
    1. 激活相关性分析
    2. 信息正交性评估
    3. 线性优化的稀疏度分配
    4. 多约束条件优化
    """

    @staticmethod
    def allocate_sparsity_with_als(giraffe_layers, target_sparsity, layer_id=0):
        """
        基于ALS方法的自适应稀疏度分配

        新的分配策略：
        1. 激活相关性矩阵分析
        2. 信息正交性评估
        3. 线性优化算法
        4. 多维约束条件

        Args:
            giraffe_layers: {layer_name: Giraffe对象} 的字典
            target_sparsity: 目标平均稀疏度
            layer_id: 层ID

        Returns:
            dict: {layer_name: 分配的稀疏度}
        """
        if not giraffe_layers:
            return {}

        layer_names = list(giraffe_layers.keys())
        num_layers = len(layer_names)

        print(f"第{layer_id}层开始ALS自适应稀疏度分配...")

        # === 1. 收集激活统计信息 ===
        activation_stats_list = []
        layer_params = {}
        total_params = 0

        for name, giraffe in giraffe_layers.items():
            activation_stats = giraffe.stats_collector.get_stats()
            activation_stats_list.append(activation_stats)

            params = giraffe.layer.weight.numel()
            layer_params[name] = params
            total_params += params

        # === 2. 计算激活相关性矩阵 ===
        correlation_matrix = ActivationCorrelationAnalyzer.compute_activation_correlation(
            activation_stats_list, layer_names
        )

        # === 3. 计算信息正交性分数 ===
        orthogonality_scores = ActivationCorrelationAnalyzer.compute_information_orthogonality(
            correlation_matrix
        )

        print(f"  激活相关性矩阵计算完成，形状: {correlation_matrix.shape}")
        print(f"  信息正交性分数范围: [{orthogonality_scores.min():.4f}, {orthogonality_scores.max():.4f}]")

        # === 4. 计算综合敏感性分数 ===
        sensitivities = {}

        for i, (name, giraffe) in enumerate(giraffe_layers.items()):
            activation_stats = activation_stats_list[i]

            # 构建相关性信息
            correlation_info = {
                'correlation_score': torch.mean(torch.abs(correlation_matrix[i, :])).item(),
                'orthogonality_score': orthogonality_scores[i].item()
            }

            # 计算综合敏感性
            sensitivity = SensitivityAnalyzer.calculate_layer_sensitivity(
                giraffe.layer, activation_stats, giraffe.layer_id, giraffe.layer_name,
                correlation_info
            )
            sensitivities[name] = sensitivity

            print(f"  {name} - 正交性: {orthogonality_scores[i]:.4f}, 敏感性: {sensitivity:.4f}")

        # === 5. 线性优化稀疏度分配 ===
        return AdaptiveSparsityAllocator._optimize_sparsity_allocation(
            sensitivities, layer_params, total_params, target_sparsity, orthogonality_scores, layer_names
        )

    @staticmethod
    def _optimize_sparsity_allocation(sensitivities, layer_params, total_params, target_sparsity,
                                    orthogonality_scores, layer_names):
        """
        使用线性优化算法分配稀疏度

        优化目标：
        1. 最小化性能损失（基于敏感性）
        2. 满足总体稀疏度约束
        3. 避免极端分配
        4. 考虑层间平衡
        """
        num_layers = len(layer_names)

        # 归一化敏感性和正交性分数
        sensitivity_values = list(sensitivities.values())
        min_sens = min(sensitivity_values)
        max_sens = max(sensitivity_values)

        if max_sens - min_sens < 1e-6:
            print(f"  敏感性分数相近，使用均匀分配")
            return {name: target_sparsity for name in layer_names}
        
        # === 线性优化分配策略 ===
        # 1. 计算重要性权重（结合敏感性和正交性）
        importance_weights = {}
        for i, name in enumerate(layer_names):
            sens = sensitivities[name]
            orth = orthogonality_scores[i].item()

            # 归一化
            normalized_sens = (sens - min_sens) / (max_sens - min_sens)
            normalized_orth = orth  # 正交性已经在[0,1]范围内

            # 综合重要性权重（正交性更重要）
            importance_weights[name] = 0.3 * normalized_sens + 0.7 * normalized_orth

        # 2. 自适应分配范围
        if target_sparsity <= 0.3:
            max_deviation = 0.12  # 低稀疏度时稍大偏差
        elif target_sparsity <= 0.5:
            max_deviation = 0.18  # 中等稀疏度时更大偏差
        else:
            max_deviation = 0.25  # 高稀疏度时最大偏差

        min_sparsity = max(0.05, target_sparsity - max_deviation)
        max_sparsity = min(0.80, target_sparsity + max_deviation)  # 提高最大稀疏度限制

        print(f"  ALS稀疏度分配范围: [{min_sparsity:.2%}, {max_sparsity:.2%}]")

        # 3. 基于重要性的反向分配
        sparsity_assignments = {}
        importance_values = list(importance_weights.values())
        min_importance = min(importance_values)
        max_importance = max(importance_values)

        for name, importance in importance_weights.items():
            if max_importance - min_importance < 1e-6:
                # 重要性相同，均匀分配
                layer_sparsity = target_sparsity
            else:
                # 重要性高的层 -> 稀疏度低
                normalized_importance = (importance - min_importance) / (max_importance - min_importance)
                sparsity_factor = 1.0 - 0.6 * normalized_importance  # 范围[0.4, 1.0]
                layer_sparsity = target_sparsity * sparsity_factor

            # 约束到允许范围
            layer_sparsity = max(min_sparsity, min(max_sparsity, layer_sparsity))
            sparsity_assignments[name] = layer_sparsity

        # 4. 线性优化调整以满足总体约束
        current_avg = sum(sparsity_assignments[name] * layer_params[name]
                         for name in sparsity_assignments.keys()) / total_params

        if abs(current_avg - target_sparsity) > 0.015:  # 偏差超过1.5%
            adjustment = target_sparsity - current_avg
            print(f"  线性优化调整，偏差: {adjustment:.2%}")

            # 基于重要性权重进行调整
            total_adjustment_capacity = 0
            for name in sparsity_assignments.keys():
                importance = importance_weights[name]
                adjustment_capacity = 1.0 - importance  # 重要性低的层调整能力强
                total_adjustment_capacity += adjustment_capacity

            # 分配调整量
            for name in sparsity_assignments.keys():
                importance = importance_weights[name]
                adjustment_capacity = 1.0 - importance
                adjustment_ratio = adjustment_capacity / (total_adjustment_capacity + 1e-8)

                layer_adjustment = adjustment * adjustment_ratio * 0.8  # 限制调整幅度
                adjusted_sparsity = sparsity_assignments[name] + layer_adjustment
                sparsity_assignments[name] = max(min_sparsity, min(max_sparsity, adjusted_sparsity))

        # 5. 最终验证和输出
        final_avg = sum(sparsity_assignments[name] * layer_params[name]
                       for name in sparsity_assignments.keys()) / total_params

        print(f"  ALS稀疏度分配结果:")
        for i, (name, sparsity) in enumerate(sparsity_assignments.items()):
            params = layer_params[name]
            pruned = int(params * sparsity)
            importance = importance_weights[name]
            orthogonality = orthogonality_scores[i].item()

            # 标注特别的分配
            note = ""
            if sparsity < target_sparsity - 0.1:
                note = " (高重要性保护)"
            elif sparsity > target_sparsity + 0.1:
                note = " (低重要性重点剪枝)"

            print(f"    {name}: {sparsity:.2%} | 重要性: {importance:.3f} | 正交性: {orthogonality:.3f}{note}")

        print(f"  最终平均稀疏度: {final_avg:.2%} (偏差: {abs(final_avg - target_sparsity):.2%})")

        return sparsity_assignments


# 保持向后兼容性的包装器
class DynamicSparsityAllocator:
    """动态稀疏度分配器 - 向后兼容包装器"""

    @staticmethod
    def allocate_sparsity(giraffe_layers, target_sparsity, layer_id=0):
        """
        动态稀疏度分配算法 - 使用新的ALS方法

        Args:
            giraffe_layers: {layer_name: Giraffe对象} 的字典
            target_sparsity: 目标平均稀疏度
            layer_id: 层ID

        Returns:
            dict: {layer_name: 分配的稀疏度}
        """
        # 使用新的ALS方法
        return AdaptiveSparsityAllocator.allocate_sparsity_with_als(
            giraffe_layers, target_sparsity, layer_id
        )
