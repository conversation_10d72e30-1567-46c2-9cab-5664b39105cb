# 基于参数活跃性感知的大模型动态内存加载系统

## 比赛信息

**题目ID**: [proj422](https://github.com/nkgongxl/Intelligent-Sparse-Decision-LLM/tree/main)
**题目名称**: 基于参数活跃性感知的大模型动态内存加载系统设计  
**所属赛道**: 2025全国大学生操作系统比赛 - "OS功能设计"赛道  
**难度等级**: 高等

### 题目背景

在大模型推理场景中，传统全量参数加载机制导致内存占用居高不下。研究表明，单次前向计算实际参与的参数仅占模型总量的20%-40%，但现有系统仍将全部参数驻留内存，造成大量冷数据占据宝贵内存资源，引发频繁换页操作并增加计算延迟。

本项目设计一种基于参数活跃性感知的动态内存管理系统，通过实时追踪模型参数的访问模式，智能识别当前推理任务所需的活跃参数子集，实现按需加载/卸载模型参数。

## 项目概述

本项目是一个专门针对 DeepSeek-R1-Distill-Qwen-1.5B 语言模型的综合性研究平台，主要关注神经网络的稀疏化技术和模型行为分析。项目实现了多种稀疏化策略，包括静态稀疏化和动态稀疏化（受 Dejavu 算法启发），以及深度的模型内部分析工具。

## 设计方案

见根目录下[计方案-提交文档.pdf](设计方案-提交文档.pdf)

## 展示视频

通过网盘分享的文件：展示视频
链接: https://pan.baidu.com/s/1Fpa6co9jrxv5dsmus6oCeQ?pwd=dsbh 提取码: dsbh

## PPT

见跟目录下[项目展示汇报.pptx](项目展示汇报.pptx)

## 主要功能

### 1. 模型稀疏化

- **静态稀疏化**：基于权重幅值的结构化稀疏化
- **动态稀疏化**：运行时自适应的神经元激活选择
- **真实模型压缩**：移除零权重参数，实现物理层面的模型压缩
- **压缩比分析**：详细的模型压缩效果统计

### 2. 模型分析

- **激活值分析**：逐层神经元激活模式研究
- **注意力模式分析**：多头注意力权重可视化
- **参数统计**：模型结构和参数分布分析

### 3. 性能评估

- **推理速度对比**：原始模型 vs 稀疏化模型 vs 压缩模型
- **质量评估**：生成文本质量对比
- **内存优化**：真实内存使用减少分析
- **资源使用统计**：内存和计算资源消耗分析

## 项目结构

```
proj422-基于参数活跃性感知的大模型动态内存加载系统设计/
├── README.md                                    # 项目主文档
├── MODEL_COMPRESSION_GUIDE.md                  # 🆕 模型压缩详细指南
├── requirements.txt                             # Python依赖列表
├── main.py                                      # 🆕 主训练脚本（支持压缩）
├── test_compressed_model.py                    # 🆕 压缩模型测试脚本
├── run_with_compression.sh                     # 🆕 带压缩功能的运行脚本
├── check_model_structure.py                    # 模型结构检查工具
├── test_deepseek_r1.py                        # 基础模型测试脚本
├── 设计方案-提交文档.pdf                        # 项目设计文档
├── 项目展示汇报.pptx                         # 项目展示汇报
├── lib/                                        # 🆕 核心库目录
│   ├── model_compressor.py                    # 🆕 模型压缩模块
│   ├── giraffe.py                             # Giraffe剪枝算法
│   ├── predictors.py                          # 预测器模块
│   ├── prune.py                               # 剪枝主模块
│   ├── data.py                                # 数据加载模块
│   ├── eval.py                                # 评估模块
│   └── ...                                   # 其他核心模块
├── DeepSeek-R1-Distill-Qwen-1.5B/             # 预训练模型文件
│   ├── config.json                            # 模型配置
│   ├── model.safetensors                      # 模型权重
│   ├── tokenizer_config.json                 # 分词器配置
│   └── ...                                   # 其他模型文件
├── analyze/                                    # 模型分析模块
│   ├── README.md                              # 分析模块文档
│   ├── analyze.py                             # 分析主程序
│   └── model_analysis/                        # 分析结果输出目录
│       ├── README.md                          # 分析结果说明
│       ├── generation_summary.json           # 生成摘要
│       ├── input_info.json                   # 输入信息
│       ├── iteration_*/                      # 各轮分析结果
│       │   ├── analysis_metadata.json        # 分析元数据
│       │   ├── generation_info.json          # 生成信息
│       │   ├── attention_patterns/           # 注意力模式
│       │   └── neuron_activations/          # 神经元激活数据
│       └── model_parameters/                 # 模型参数分析
├── dynamic_sparse/                            # 动态稀疏化模块
│   ├── README.md                             # 动态稀疏化文档
│   ├── __init__.py                           # 模块初始化
│   ├── demo.py                               # 主演示脚本
│   ├── dynamic_sparse_model.py               # 动态稀疏化模型核心
│   ├── dynamic_optimization_mlp.py           # 动态优化MLP层
│   ├── dynamic_optimization_predictor.py     # 动态优化预测器
│   ├── utils.py                              # 工具函数
│   └── dynamic_optimization_results.json     # 演示结果
├── static_sparse/                             # 静态稀疏化模块
│   ├── README.md                             # 静态稀疏化文档
│   ├── static_sparse_demo.py                # 基础演示脚本
│   ├── static_sparse_comparison.py          # 性能对比脚本
│   ├── sparse_demo_results.json             # 演示结果
│   └── sparse_comparison_results.json       # 对比结果
├── c4_cache/                                 # C4数据集缓存
│   ├── training_*.json                       # 训练数据缓存
│   ├── validation_*.json                     # 验证数据缓存
│   └── test_*.json                          # 测试数据缓存
├── dynamic_optimization_predictors/          # 预测器权重存储
│   └── predictor_layer_*.pth                # 各层预测器权重
└── figures/                                  # 生成图表
    ├── c4_perplexity_comparison.png         # 困惑度对比
    ├── generation_speed_comparison.png      # 生成速度对比
    ├── per_layer_activation_ratio.png       # 分层激活率
    └── param_compression.png                # 参数压缩比
```

## 快速开始

### 环境准备

1. **克隆项目**

```bash
git clone <repository-url>
cd OScompetition
```

2. **安装依赖**

```bash
pip install -r requirements.txt
```

3. **下载模型**

```bash
# 确保 DeepSeek-R1-Distill-Qwen-1.5B 模型文件在正确位置
# 模型应包含：config.json, model.safetensors, tokenizer 等文件
```

### 基础使用

1. **模型结构检查**

```bash
python check_model_structure.py
```

2. **基础推理测试**

```bash
python test_deepseek_r1.py
```

3. **模型深度分析**

```bash
cd analyze
python analyze.py --save_plots --num_inferences 5 --max_neurons_to_save 100
```

4. **动态稀疏化演示**

```bash
cd dynamic_sparse
python demo.py
```

5. **静态稀疏化演示**

```bash
cd static_sparse
python static_sparse_demo.py
```

6. **静态稀疏化对比测试**

```bash
cd static_sparse
python static_sparse_comparison.py
```

## 🆕 真实模型压缩功能

本项目现在支持真正的模型压缩，不仅将权重设置为0，还能完全移除零权重参数，实现物理层面的内存和存储优化。

### 使用方法

1. **基本压缩训练**

```bash
python main.py \
    --model deepseek-coder-1.3b-base \
    --sparsity_ratio 0.5 \
    --prune_method giraffe \
    --compress_model \
    --save_model my_compressed_model
```

2. **使用便捷脚本**

```bash
./run_with_compression.sh
```

3. **测试压缩效果**

```bash
python test_compressed_model.py \
    --compressed_model final_compressed_model_compressed \
    --test_ppl \
    --test_speed
```

### 压缩效果

- **50%稀疏度**: 约2x内存减少
- **70%稀疏度**: 约3.3x内存减少  
- **80%稀疏度**: 约5x内存减少

详细使用说明请参考 [MODEL_COMPRESSION_GUIDE.md](MODEL_COMPRESSION_GUIDE.md)

## 稀疏化技术对比

| 技术类型   | 实现策略         | 压缩比 | 推理速度提升  | 质量保持      | 内存占用 | 适用场景     |
| ---------- | ---------------- | ------ | ------------- | ------------- | -------- | ------------ |
| 静态稀疏化 | 权重幅值筛选     | 2-4x   | 中等 (1.2-2x) | 较好 (90-95%) | 低       | 资源受限环境 |
| 动态稀疏化 | 上下文自适应预测 | 1.5-3x | 高 (1.5-3x)   | 优秀 (95%+)   | 中等     | 实时推理系统 |
| **真实压缩** | **稀疏矩阵存储** | **2-5x** | **高 (1.5-3x)** | **优秀 (95%+)** | **很低** | **生产部署** |

### 具体性能指标

**动态稀疏化** (基于demo.py实际测试结果):

- 激活率配置: 50%-90% 可调
- 平均加速比: 2.5x
- 困惑度保持: 95%以上
- 支持分层配置和多种策略

**静态稀疏化** (基于static_sparse_demo.py):

- 稀疏率: 75% (保留25%权重)
- 固定压缩比: 4x
- 实现简单，部署友好

## 研究成果

### 关键发现

1. **动态稀疏化效果**：在保持95%以上质量的前提下，实现了平均2.5x的推理加速
2. **层级影响分析**：发现不同层的稀疏化敏感性存在显著差异
3. **注意力模式**：揭示了稀疏化对多头注意力权重分布的影响规律

### 技术创新

- 实现了类似Dejavu的动态神经元选择机制
- 开发了自适应稀疏性预测器
- 建立了完整的模型行为分析框架

## 性能指标

### 计算效率

- **内存使用减少**：30-50%
- **推理速度提升**：1.5-3x
- **模型大小压缩**：2-4x

### 质量维持

- **BLEU分数保持**：95%+
- **语义相似度**：0.92+
- **困惑度增加**：<10%

## 技术栈

- **深度学习框架**：PyTorch, Transformers
- **数值计算**：NumPy, SciPy
- **数据分析**：Pandas, Matplotlib, Seaborn
- **模型加速**：CUDA, BitsAndBytes
- **存储格式**：JSON, Pickle, CSV

## 使用说明

### 参数配置

各模块都支持灵活的参数配置，主要参数包括：

- `k_ratio`：稀疏化比例（0.1-0.8）
- `sparsity_strategy`：稀疏化策略（uniform/adaptive）
- `predictor_low_rank`：预测器低秩维度
- `max_neurons_to_save`：保存的最大神经元数量

### 输出说明

项目会生成以下类型的输出：

- **JSON文件**：结构化的分析结果
- **CSV文件**：神经元激活数据
- **图表文件**：可视化分析图表
- **性能报告**：详细的性能对比数据

## 许可证

本项目采用  - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系信息

- **项目维护者**：姜宇
- **邮箱**：<EMAIL>
- **项目主页**：[https://gitlab.eduxiji.net/T202510055996260/*********************](https://gitlab.eduxiji.net/T202510055996260/*********************)
